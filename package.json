{"name": "tomprav.dev", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.3.0", "@stable-canvas/comfyui-client": "^1.4.1", "gray-matter": "^4.0.3", "marked": "^15.0.8", "next": "15.3.0", "next-themes": "^0.4.6", "postcss": "^8.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "remark": "^15.0.1", "remark-html": "^16.0.1", "replicate": "^1.0.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.0", "tailwindcss": "^4.1.4", "typescript": "^5"}}