# Tom <PERSON>tz - Portfolio Website

A modern portfolio website showcasing AI tools, experiments, and development projects.

## 🚀 Features

- **Lost Pages Generator**: AI-powered 404 pages with creative stories
- **Project Showcase**: Detailed project pages with tech stacks and demos
- **Blog System**: Markdown-based blog with reading time estimates
- **Dark/Light Theme**: Seamless theme switching
- **Responsive Design**: Mobile-first design with Tailwind CSS

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Styling**: Tailwind CSS 4
- **Content**: MDX for blog posts
- **AI Integration**: Mistral API for content generation
- **Deployment**: Vercel

## 🏃‍♂️ Getting Started

1. **Clone the repository**:
   ```bash
   git clone https://github.com/tompravetz/tomprav.dev.git
   cd tomprav.dev
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env.local
   # Add your Mistral API key and other environment variables
   ```

4. **Run the development server**:
   ```bash
   npm run dev
   ```

5. **Open [http://localhost:3000](http://localhost:3000)** in your browser.

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── api/               # API routes
│   ├── blog/              # Blog pages
│   ├── projects/          # Project showcase pages
│   └── globals.css        # Global styles
├── components/            # Reusable React components
├── data/                  # Static data (projects, etc.)
└── lib/                   # Utility functions and engines

public/
├── projects/              # Project images and assets
└── blog/                  # Blog post images
```

## 🎯 Key Projects

### Lost Pages Generator
AI-powered 404 pages that turn broken links into creative stories using Mistral AI.

### CoHost.AI
A sophisticated AI streaming companion for Twitch with real-time chat integration, voice synthesis, and OBS Studio integration.

## 🚀 Deployment

This project is deployed on Vercel. Any push to the main branch automatically triggers a new deployment.

## 📝 License

This project is open source and available under the [MIT License](LICENSE).
