import Link from 'next/link'
import Image from 'next/image'

type ProjectCardProps = {
  title: string
  description: string
  href: string
  image: string
  stack: string[]
}

export default function ProjectCard({ title, description, href, image, stack }: ProjectCardProps) {
  return (
    <Link
      href={href}
      className="block rounded-xl border border-zinc-200 dark:border-zinc-700 bg-white dark:bg-zinc-800 shadow-sm hover:shadow-md transition overflow-hidden"
    >
      <Image
        src={image}
        alt={title}
        width={640}
        height={360}
        className="object-cover w-full h-40 sm:h-48 md:h-56"
      />
      <div className="p-4">
        <h3 className="text-lg font-semibold text-zinc-900 dark:text-white">{title}</h3>
        <p className="mt-2 text-sm text-zinc-600 dark:text-zinc-400">{description}</p>
        <div className="flex flex-wrap gap-2 mt-3">
          {stack.map((tech) => (
            <span
              key={tech}
              className="text-xs bg-zinc-200 dark:bg-zinc-700 text-zinc-800 dark:text-zinc-200 px-2 py-1 rounded-full"
            >
              {tech}
            </span>
          ))}
        </div>
        <span className="mt-4 inline-block text-sm text-indigo-600 dark:text-indigo-400 font-medium hover:underline">
          View →
        </span>
      </div>
    </Link>
  )
}
