// @ts-ignore: mdx default export isn't typed properly
import mdx from '@next/mdx'
import type { NextConfig } from 'next'

// Create MDX plugin
const withMDX = mdx()

// Define the Next.js configuration
const baseConfig: NextConfig = {
  // Improve production performance
  reactStrictMode: true,

  // Optimize image loading
  images: {
    formats: ['image/avif', 'image/webp'],
    minimumCacheTTL: 60,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
      },
    ],
  },

  // Configure Turbopack (fix the warning)
  // Note: We're not using the deprecated 'experimental.turbo' property

  // Experimental features
  experimental: {
    // Keep MDX support
    mdxRs: true,
  },

  // Ignore TypeScript and ESLint errors during build for now
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Optimize for production
  productionBrowserSourceMaps: false,

  // Add headers for better security and performance
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
    ];
  },
}

// Apply MDX plugin to the configuration
export default withMDX(baseConfig)