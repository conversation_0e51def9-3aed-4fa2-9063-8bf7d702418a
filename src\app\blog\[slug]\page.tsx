import { getPostBySlug } from '@/lib/blog'
import { notFound } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'

export default async function BlogPostPage({ params }: { params: { slug: string } }) {
  const { slug } = params
  const post = await getPostBySlug(slug)
  if (!post) return notFound()

  return (
    <div className="bg-zinc-100 dark:bg-zinc-950 min-h-screen w-full">
      <div className="flex flex-col min-h-screen max-w-3xl mx-auto w-full bg-white dark:bg-zinc-900 text-zinc-900 dark:text-white px-6 sm:px-10 py-20">

        <Link
          href="/blog"
          className="text-sm text-indigo-600 dark:text-indigo-400 hover:underline mb-6 inline-block"
        >
          ← Back to blog
        </Link>

        <h1 className="text-4xl font-bold leading-tight tracking-tight mb-2">
          {post.title}
        </h1>

        <div className="text-sm text-zinc-500 dark:text-zinc-400 mb-6 flex flex-wrap items-center gap-3">
          <span>{new Date(post.date).toLocaleDateString()}</span>
          <span>·</span>
          <span>{post.readTime}</span>
          {post.tags?.map((tag: string) => (
            <span
              key={tag}
              className="bg-indigo-100 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300 text-xs font-medium px-2 py-0.5 rounded-full"
            >
              {tag}
            </span>
          ))}
        </div>

        <div className="h-px w-full bg-gradient-to-r from-transparent via-zinc-300 to-transparent dark:via-zinc-700 mb-10" />

        {post.coverImage && (
          <div className="mb-12 overflow-hidden rounded-xl shadow-md">
            <Image
              src={typeof post.coverImage === 'string' ? post.coverImage : post.coverImage.url}
              alt=""
              width={800}
              height={400}
              className="w-full object-cover h-64"
            />
          </div>
        )}

        <article
          className="prose prose-zinc dark:prose-invert prose-lg max-w-none leading-relaxed"
          dangerouslySetInnerHTML={{ __html: post.contentHtml }}
        />
      </div>
    </div>
  )
}
