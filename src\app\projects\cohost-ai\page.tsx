import Image from 'next/image'
import Link from 'next/link'

export default function CoHostAIProject() {
  return (
    <main className="max-w-5xl mx-auto px-6 py-16 space-y-20">
      {/* Hero */}
      <section className="flex flex-col-reverse md:flex-row items-center gap-12">
        <div className="space-y-6 max-w-xl">
          <h1 className="text-4xl font-extrabold">CoHost.AI</h1>
          <p className="text-lg text-muted-foreground">
            A sophisticated AI streaming companion that provides real-time interaction through voice recognition, 
            AI responses, and text-to-speech synthesis. Built for professional streaming environments.
          </p>
          <div className="flex gap-4">
            <Link
              href="https://github.com/tompravetz/cohost.ai"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block bg-violet-600 text-white px-5 py-2.5 rounded-lg shadow hover:bg-violet-500 transition"
            >
              🔗 View on GitHub
            </Link>
          </div>
        </div>

        <div className="w-full md:w-1/2 rounded-xl overflow-hidden shadow-lg">
          <Image
            src="/projects/cohost-ai/cohost-ai-hero.png"
            alt="CoHost.AI streaming interface"
            width={800}
            height={600}
            className="w-full h-auto object-cover"
          />
        </div>
      </section>

      {/* Key Features */}
      <section className="grid md:grid-cols-2 gap-12">
        <div>
          <h2 className="text-2xl font-bold mb-4">🌟 Key Features</h2>
          <ul className="list-disc list-inside space-y-2 text-muted-foreground">
            <li>Real-time Twitch chat integration via Streamer.bot</li>
            <li>Character-driven AI responses using Ollama</li>
            <li>High-quality Google Cloud Text-to-Speech</li>
            <li>Push-to-talk voice recognition</li>
            <li>Automatic OBS Studio scene management</li>
            <li>Intelligent TTS caching for performance</li>
            <li>Beautiful CLI interface with live monitoring</li>
          </ul>
        </div>

        <div>
          <h2 className="text-2xl font-bold mb-4">🧠 Tech Stack</h2>
          <div className="flex flex-wrap gap-3">
            <span className="bg-zinc-100 dark:bg-zinc-800 px-4 py-1 rounded-full text-sm">🐍 Python</span>
            <span className="bg-zinc-100 dark:bg-zinc-800 px-4 py-1 rounded-full text-sm">🧠 Ollama</span>
            <span className="bg-zinc-100 dark:bg-zinc-800 px-4 py-1 rounded-full text-sm">🗣️ Google Cloud TTS</span>
            <span className="bg-zinc-100 dark:bg-zinc-800 px-4 py-1 rounded-full text-sm">📺 OBS Studio</span>
            <span className="bg-zinc-100 dark:bg-zinc-800 px-4 py-1 rounded-full text-sm">🎮 Streamer.bot</span>
            <span className="bg-zinc-100 dark:bg-zinc-800 px-4 py-1 rounded-full text-sm">🎤 Speech Recognition</span>
          </div>
        </div>
      </section>

      {/* Architecture */}
      <section className="bg-zinc-100 dark:bg-zinc-900 p-8 rounded-xl shadow space-y-6">
        <h2 className="text-2xl font-bold">🏗️ System Architecture</h2>
        <p className="text-muted-foreground">
          CoHost.AI operates as a real-time streaming companion with multiple integrated components:
        </p>

        <div className="bg-white dark:bg-zinc-800 p-6 rounded-lg shadow space-y-4">
          <div className="font-mono text-sm text-center space-y-2">
            <div className="flex justify-center items-center gap-4">
              <div className="bg-blue-100 dark:bg-blue-900 px-3 py-1 rounded">Streamer.bot</div>
              <span>→</span>
              <div className="bg-green-100 dark:bg-green-900 px-3 py-1 rounded">CoHost.AI</div>
              <span>→</span>
              <div className="bg-purple-100 dark:bg-purple-900 px-3 py-1 rounded">OBS Studio</div>
            </div>
            <div className="text-xs text-muted-foreground">(UDP Sender) → (Main System) → (Scene Management)</div>
            <div className="flex justify-center items-center gap-4 mt-4">
              <div className="bg-orange-100 dark:bg-orange-900 px-3 py-1 rounded">Google Cloud TTS</div>
              <span>→</span>
              <div className="bg-red-100 dark:bg-red-900 px-3 py-1 rounded">Audio Output</div>
            </div>
          </div>
        </div>
      </section>

      {/* Performance Metrics */}
      <section className="grid md:grid-cols-3 gap-8">
        <div className="text-center space-y-2">
          <div className="text-3xl font-bold text-violet-400">~2-3s</div>
          <div className="text-sm text-muted-foreground">TTS Response Time</div>
          <div className="text-xs text-muted-foreground">(~0.1s with caching)</div>
        </div>
        <div className="text-center space-y-2">
          <div className="text-3xl font-bold text-green-400">&lt;100ms</div>
          <div className="text-sm text-muted-foreground">Audio Latency</div>
          <div className="text-xs text-muted-foreground">(for cached responses)</div>
        </div>
        <div className="text-center space-y-2">
          <div className="text-3xl font-bold text-blue-400">50-100MB</div>
          <div className="text-sm text-muted-foreground">Memory Usage</div>
          <div className="text-xs text-muted-foreground">(typical operation)</div>
        </div>
      </section>

      {/* Character Customization */}
      <section className="space-y-4">
        <h2 className="text-2xl font-bold">🎭 AI Character Customization</h2>
        <p className="text-muted-foreground">
          CoHost.AI uses a simple text file to define your AI character's personality and behavior. 
          Create anything from a sarcastic gaming buddy to a professional streaming assistant.
        </p>

        <div className="grid md:grid-cols-2 gap-6">
          <div className="bg-zinc-100 dark:bg-zinc-900 p-6 rounded-lg">
            <h3 className="font-bold mb-2">🎮 Sarcastic Gaming Buddy</h3>
            <p className="text-sm text-muted-foreground italic">
              "You are SnarkyBot, a witty gaming co-host who's seen it all. 
              You're sarcastic but never mean-spirited, and you love roasting bad gameplay."
            </p>
          </div>
          <div className="bg-zinc-100 dark:bg-zinc-900 p-6 rounded-lg">
            <h3 className="font-bold mb-2">💼 Professional Assistant</h3>
            <p className="text-sm text-muted-foreground italic">
              "You are StreamAssistant, a professional and knowledgeable co-host. 
              You provide helpful information and maintain a polished stream environment."
            </p>
          </div>
        </div>
      </section>

      {/* Installation & Setup */}
      <section className="space-y-4">
        <h2 className="text-2xl font-bold">⚙️ Quick Setup</h2>
        <p className="text-muted-foreground">
          Getting started with CoHost.AI requires a few key components:
        </p>
        
        <div className="bg-zinc-900 p-4 rounded-lg overflow-x-auto">
          <pre className="text-sm text-zinc-200">
{`# Clone and setup
git clone https://github.com/tompravetz/cohost.ai
cd cohost.ai
python -m venv venv
venv\\Scripts\\activate
pip install -r requirements.txt

# Setup Ollama
ollama pull mistral

# Configure environment
cp .env.example .env
# Edit .env with your Google Cloud credentials

# Run the application
python run.py`}
          </pre>
        </div>
      </section>

      {/* What's Next */}
      <section>
        <h2 className="text-2xl font-bold mb-4">🔮 Future Enhancements</h2>
        <ul className="list-disc list-inside space-y-2 text-muted-foreground">
          <li>Multi-language support for international streamers</li>
          <li>Advanced emotion detection and response adaptation</li>
          <li>Integration with more streaming platforms (YouTube, Discord)</li>
          <li>Custom voice training for personalized TTS</li>
          <li>Advanced analytics and chat sentiment analysis</li>
        </ul>
      </section>
    </main>
  )
}
