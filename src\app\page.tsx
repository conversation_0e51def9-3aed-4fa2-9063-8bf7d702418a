import Link from 'next/link';

export default function Home() {
  return (
    <main className="min-h-[calc(100vh-4rem)] flex flex-col items-center justify-center px-6 text-center">
      <h1 className="text-5xl sm:text-6xl font-extrabold tracking-tight text-zinc-900 dark:text-white">
        Hi, I’m <PERSON>.
      </h1>
      <p className="mt-4 max-w-xl text-lg sm:text-xl text-zinc-600 dark:text-zinc-400">
        I build AI tools that solve real problems — and a few that don’t.
      </p>
      <div className="mt-8 flex flex-col sm:flex-row items-center gap-4">
        <Link href="/projects" className="px-6 py-3 rounded-md bg-indigo-600 text-white font-medium hover:bg-indigo-700 transition">
          View My Work
        </Link>
        <Link href="/blog" className="px-6 py-3 rounded-md border border-zinc-300 dark:border-zinc-700 text-zinc-800 dark:text-white hover:bg-zinc-100 dark:hover:bg-zinc-800 transition">
          Read the Blog
        </Link>
      </div>
    </main>
  )
}
