import Link from 'next/link'
import Image from 'next/image'
import { getAllPosts } from '@/lib/blog'

export default async function BlogIndexPage() {
  const posts = await getAllPosts()
  const featured = posts.find((p) => p.featured)

  return (
    <main className="max-w-3xl mx-auto px-6 py-16">
      <h1 className="text-4xl font-bold mb-2 text-zinc-900 dark:text-white">Blog</h1>
      <p className="text-zinc-500 dark:text-zinc-400 mb-10 text-lg">
        Thoughts on AI, tools, and side projects that sometimes escape control.
      </p>

      {/* You can add tag filtering here later with client component */}

      {featured && (
        <Link href={`/blog/${featured.slug}`} className="block rounded-xl border border-indigo-300 dark:border-indigo-600 p-6 mb-10 bg-white dark:bg-zinc-900 shadow-sm hover:shadow-md transition">
          <span className="inline-block mb-2 text-xs font-semibold uppercase text-indigo-500 bg-indigo-100 dark:bg-indigo-800 px-2 py-0.5 rounded-full">
            Featured
          </span>
          <h2 className="text-2xl font-bold text-indigo-700 dark:text-indigo-300">{featured.title}</h2>
          <p className="text-sm text-zinc-600 dark:text-zinc-400 mt-1">
            {new Date(featured.date).toLocaleDateString()} · {featured.readTime}
          </p>
          <p className="mt-2 text-zinc-800 dark:text-zinc-200">{featured.excerpt}</p>
        </Link>
      )}

      <ul className="space-y-10">
      {posts
      .filter((post) => post.slug !== featured?.slug)
      .map((post) => (
          <li key={post.slug} className="group border border-zinc-200 dark:border-zinc-700 rounded-xl overflow-hidden hover:shadow-md transition-shadow">
            <Link href={`/blog/${post.slug}`} className="block p-6 hover:bg-zinc-50 dark:hover:bg-zinc-900 transition-colors">
              {post.coverImage && (
                <div className="mb-4 -mx-6 -mt-6">
                  <Image
                    src={post.coverImage}
                    alt=""
                    width={800}
                    height={400}
                    className="w-full object-cover h-48 rounded-t-xl"
                  />
                </div>
              )}
              <h2 className="text-2xl font-semibold text-zinc-900 dark:text-white group-hover:text-indigo-600 transition">
                {post.title}
              </h2>
              <p className="text-sm text-zinc-500 dark:text-zinc-400 mt-1">
                {new Date(post.date).toLocaleDateString()} · {post.readTime}
              </p>
              <p className="mt-2 text-zinc-700 dark:text-zinc-300">{post.excerpt}</p>
              {post.tags && (
                <div className="flex gap-2 mt-3 flex-wrap">
                  {post.tags.map((tag) => (
                    <span
                      key={tag}
                      className="text-xs border border-indigo-300 dark:border-indigo-600 px-2 py-0.5 rounded-full text-indigo-700 dark:text-indigo-300"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}
              <span className="inline-block mt-4 text-sm text-indigo-600 dark:text-indigo-400 font-medium hover:underline">
                Read more →
              </span>
            </Link>
          </li>
        ))}
      </ul>
    </main>
  )
}
