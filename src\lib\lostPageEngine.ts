import { lostPagesCache } from './cache'

// Define the LostPage interface
export interface LostPage {
  title: string;
  reason: string;
  classification: string;
  recoveryTip: string;
}

// API configuration
const MISTRAL_API = 'https://api.mistral.ai/v1/chat/completions';
const MISTRAL_MODEL = 'mistral-small-latest'; // Using Mistral Small 25.01

// Timeouts (optimized for Vercel's 10s limit)
const API_TIMEOUT_MS = 3000; // Reduced to 3 seconds

// Helper function to implement timeout for fetch requests
async function fetchWithTimeout(url: string, options: RequestInit, timeoutMs: number): Promise<Response> {
  const controller = new AbortController();
  const { signal } = controller;
  
  const timeout = setTimeout(() => controller.abort(), timeoutMs);
  
  try {
    const response = await fetch(url, { ...options, signal });
    clearTimeout(timeout);
    return response;
  } catch (error) {
    clearTimeout(timeout);
    throw error;
  }
}

// Fallback responses for when the API is unavailable or times out
const FALLBACK_RESPONSES: LostPage[] = [
  {
    title: "Quantum Entanglement: Page Particles Scattered",
    reason: "This page's quantum state collapsed when observed.",
    classification: "Heisenberg Uncertainty Error",
    recoveryTip: "Try looking away and then looking back very quickly."
  },
  {
    title: "Digital Amnesia: Memory Overflow",
    reason: "The server forgot what it was supposed to remember.",
    classification: "Cognitive Buffer Error",
    recoveryTip: "Have you tried showing it pictures of its younger days?"
  },
  {
    title: "Cosmic Void: Page Sucked Into Black Hole",
    reason: "A miniature black hole formed in our database.",
    classification: "Gravitational Anomaly",
    recoveryTip: "Wait for Hawking radiation to slowly return the data."
  },
  {
    title: "Temporal Paradox: Page Exists in Another Timeline",
    reason: "You've stumbled into the wrong branch of the multiverse.",
    classification: "Parallel Universe Shift",
    recoveryTip: "Try accessing this URL after making a different life choice."
  },
  {
    title: "Reality Glitch: Page Matrix Malfunction",
    reason: "There was a hiccup in the simulation we all live in.",
    classification: "Existential Error Code",
    recoveryTip: "Take the red pill and try again."
  }
];

// Get a deterministic but varied fallback response based on the slug
function getFallbackResponse(slug: string): LostPage {
  const slugHash = slug.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  return FALLBACK_RESPONSES[slugHash % FALLBACK_RESPONSES.length];
}

// Function to generate content using Mistral API
async function generateLostPageContent(slug: string): Promise<string> {
  const apiKey = process.env.MISTRAL_API_KEY;
  
  if (!apiKey) {
    throw new Error('Mistral API key not found in environment variables');
  }
  
  const res = await fetchWithTimeout(MISTRAL_API, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify({
      model: MISTRAL_MODEL,
      messages: [
        {
          role: "user",
          content: `You are a whimsical AI who returns creative 404 page messages. Your output must always be valid JSON with the following four fields:

- "title": a silly title (under 12 words)
- "reason": a short reason why the page is missing
- "classification": a funny category like "Temporal Anomaly"
- "recoveryTip": a joke suggestion to recover the page

If the URL slug includes inappropriate, hateful, or explicit language, DO NOT mention it. Just make a generic joke about reality breaking down, censorship, or forbidden knowledge. Never refuse the request. Just use silly fiction.

Always respond ONLY with valid JSON. Do not include markdown or triple backticks.

Example:
{
  "title": "Reality Overflow: Page Poured Into Another Universe",
  "reason": "A curious temporal raccoon unplugged the stability protocols.",
  "classification": "Dimensional Glitch",
  "recoveryTip": "Try distracting it with cosmic peanut butter."
}

The slug is ${slug}`
        }
      ],
      temperature: 0.7,
      max_tokens: 300
    }),
  }, API_TIMEOUT_MS);

  if (!res.ok) {
    throw new Error(`Mistral API returned status ${res.status}`);
  }

  const data = await res.json();
  return data.choices[0].message.content;
}

// Main function to generate a lost page for a given slug
export async function generateLostPage(slug: string): Promise<LostPage | { error: string }> {
  // Check cache first
  const cachedPage = lostPagesCache.get(slug);
  if (cachedPage) {
    return cachedPage;
  }
  
  // Set up a timeout for the entire function to ensure we return within Vercel's limits
  const functionTimeoutMs = 4000; // 4 seconds to stay well under Vercel's 10s limit
  const functionTimeoutPromise = new Promise<LostPage>(resolve => {
    setTimeout(() => {
      // If we're about to time out, return a fallback response
      const fallback = getFallbackResponse(slug);
      resolve(fallback);
    }, functionTimeoutMs);
  });

  try {
    // Race between the normal generation process and the function timeout
    const result = await Promise.race<LostPage | string>([
      // Normal generation process
      (async (): Promise<string> => {
        try {
          return await generateLostPageContent(slug);
        } catch (apiError) {
          // Fallback to a pre-generated response if the API is unavailable
          throw apiError;
        }
      })(),
      // Timeout fallback
      functionTimeoutPromise
    ]);

    // If we got a full LostPage object from the timeout, return it
    if (typeof result === 'object' && 'title' in result) {
      // Cache the fallback result
      lostPagesCache.set(slug, result);
      return result;
    }

    // Otherwise, we got the raw text from the LLM
    const rawText = result as string;

    // More robust JSON parsing
    let parsed;
    try {
      // First try direct parsing
      parsed = JSON.parse(rawText.trim());
    } catch (parseError) {
      // If that fails, try to extract JSON from markdown code blocks
      const match = rawText.match(/```(?:json)?\\s*([\\s\\S]*?)\\s*```/i);
      if (!match) {
        // If no code block, try to clean up the text and parse again
        const cleanJson = rawText
          .replace(/^\\s*{\\s*/, '{')   // remove leading space/braces/newlines
          .replace(/\\s*}\\s*$/, '}')   // remove trailing space/braces/newlines
          .trim();
        parsed = JSON.parse(cleanJson);
      } else {
        parsed = JSON.parse(match[1].trim());
      }
    }

    const pageResult: LostPage = {
      title: parsed.title,
      reason: parsed.reason,
      classification: parsed.classification,
      recoveryTip: parsed.recoveryTip
    };

    // Cache the result
    lostPagesCache.set(slug, pageResult);

    return pageResult;
  } catch (err) {
    // If there was an error in the main process, return a fallback
    const fallback = getFallbackResponse(slug);
    lostPagesCache.set(slug, fallback);
    return fallback;
  }
}

// Function to generate a random slug for the "Discover Another" button
export async function generateLostSlug(): Promise<string> {
  try {
    // Get the API key from environment variables
    const apiKey = process.env.MISTRAL_API_KEY;

    if (!apiKey) {
      throw new Error('Mistral API key not found in environment variables');
    }

    const res = await fetchWithTimeout(MISTRAL_API, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: MISTRAL_MODEL,
        messages: [
          {
            role: "user",
            content: `You are a slug generator. Return a single fake but plausible URL slug for a lost webpage. No quotes. No explanation. No domain name. No slashes. Just the slug. Example format: haunted-library-catapult`
          }
        ],
        temperature: 0.7,
        max_tokens: 20
      }),
    }, API_TIMEOUT_MS);

    if (!res.ok) {
      throw new Error(`Mistral API returned status ${res.status}`);
    }

    const data = await res.json();
    const raw = data.choices[0].message.content;

    // Clean up the slug
    const cleanSlug = raw
      .trim()
      .replace(/^["']/, '')  // remove leading quote if exists
      .replace(/["']$/, '')  // remove trailing quote if exists
      .replace(/[^\w\-\/]/g, '-')  // replace any weird characters with dashes
      .toLowerCase();

    return cleanSlug || 'mysterious-page';
  } catch (error) {
    // Fallback to a default slug if generation fails
    const fallbackSlugs = [
      'mysterious-page',
      'lost-in-cyberspace',
      'digital-void',
      'quantum-glitch',
      'reality-error'
    ];
    return fallbackSlugs[Math.floor(Math.random() * fallbackSlugs.length)];
  }
}
