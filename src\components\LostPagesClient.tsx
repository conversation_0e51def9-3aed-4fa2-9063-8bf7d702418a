'use client'

import { useEffect, useState } from 'react'
import { usePathname } from 'next/navigation'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

type LostPageAI = {
  title: string
  reason: string
  classification: string
  recoveryTip: string
  imagePrompt?: string // Made optional since we're not using it
  imageUrl?: string
}

export default function LostPagesClient() {
  const pathname = usePathname()
  const slug = pathname?.replace(/^\/+/, '') || ''
  const [lostPage, setLostPage] = useState<LostPageAI | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  useEffect(() => {
    let isMounted = true;
    const controller = new AbortController();
    const { signal } = controller;

    async function loadPage() {
      try {
        // Reset states when loading a new page
        if (isMounted) {
          setLoading(true);
          setError(null);
        }

        // Add timeout to the fetch request
        const timeoutId = setTimeout(() => controller.abort(), 15000);

        const res = await fetch('/api/lost-page', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ slug }),
          signal,
        })

        clearTimeout(timeoutId);

        if (!res.ok) {
          throw new Error(`Server returned ${res.status}: ${res.statusText}`);
        }

        const data = await res.json()

        if (data.error) throw new Error(data.error)

        if (isMounted) {
          setLostPage(data)
        }
      } catch (err: unknown) {
        if (!isMounted) return;

        if (err instanceof Error && err.name === 'AbortError') {
          setError('Request timed out. Please try again.');
        } else if (err instanceof Error) {
          setError(`Error: ${err.message}`);
        } else {
          setError('Something went wrong while contacting the imagination engine.');
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    }

    loadPage();

    // Cleanup function
    return () => {
      isMounted = false;
      controller.abort();
    };
  }, [slug])

  return (
    <main className="min-h-screen flex items-center justify-center px-6 py-12 bg-white dark:bg-neutral-900 text-neutral-900 dark:text-neutral-100 transition-colors">
      <div className="max-w-xl w-full space-y-6 text-center">
        {loading && (
          <div className="space-y-6">
            <div className="animate-pulse text-sm italic text-neutral-500 dark:text-neutral-400">
              Summoning the lost page...
            </div>

            {/* Loading skeleton */}
            <div className="w-full h-48 rounded-2xl overflow-hidden bg-neutral-200 dark:bg-neutral-800 animate-pulse"></div>
            <div className="h-8 bg-neutral-200 dark:bg-neutral-800 rounded-lg w-3/4 mx-auto animate-pulse"></div>
            <div className="h-16 bg-neutral-200 dark:bg-neutral-800 rounded-lg w-full animate-pulse"></div>
            <div className="h-4 bg-neutral-200 dark:bg-neutral-800 rounded-lg w-1/2 mx-auto animate-pulse"></div>
          </div>
        )}

        {error && !loading && (
          <div className="space-y-4">
            <div className="text-red-500 dark:text-red-400 p-4 border border-red-300 dark:border-red-800 rounded-lg bg-red-50 dark:bg-red-900/20">
              {error}
            </div>
            <div className="flex justify-center gap-4 pt-4">
              <Link href="/" className="px-4 py-2 rounded-xl border border-neutral-400 dark:border-neutral-700 hover:bg-neutral-100 dark:hover:bg-neutral-800 transition">
                Return Home
              </Link>
              <button
                onClick={() => router.refresh()}
                className="px-4 py-2 rounded-xl border border-neutral-400 dark:border-neutral-700 hover:bg-neutral-100 dark:hover:bg-neutral-800 transition"
                >
                Try Again
              </button>
            </div>
          </div>
        )}

        {lostPage && !loading && (
          <>
            {/* Image section removed for better performance */}

            <h1 className="text-3xl sm:text-4xl font-bold tracking-tight">
              {lostPage.title}
            </h1>

            <p className="text-base sm:text-lg text-neutral-700 dark:text-neutral-300">
              {lostPage.reason}
            </p>

            <div className="text-sm text-neutral-500 dark:text-neutral-400 space-y-1">
              <p>{lostPage.classification}</p>
              <p className="italic">💡 {lostPage.recoveryTip}</p>
            </div>

            <div className="flex justify-center gap-4 pt-4">
              <Link href="/" className="px-4 py-2 rounded-xl border border-neutral-400 dark:border-neutral-700 hover:bg-neutral-100 dark:hover:bg-neutral-800 transition">
                Return Home
              </Link>
              <button
                onClick={async () => {
                  try {
                    setLoading(true);
                    const res = await fetch(`/api/generate-slug?current=${slug}`)
                    if (!res.ok) throw new Error('Failed to generate new slug');

                    const data = await res.json()
                    if (data.error) throw new Error(data.error);

                    if (data.slug) {
                      router.push(`/${data.slug}`)
                    }
                  } catch (err) {
                    setError('Failed to discover a new page. Please try again.');
                    setLoading(false);
                  }
                }}
                className="px-4 py-2 rounded-xl border border-neutral-400 dark:border-neutral-700 hover:bg-neutral-100 dark:hover:bg-neutral-800 transition"
                disabled={loading}
                >
                {loading ? 'Loading...' : 'Discover Another'}
              </button>
            </div>
          </>
        )}
      </div>
    </main>
  )
}
