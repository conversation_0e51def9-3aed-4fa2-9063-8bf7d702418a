'use client'

import LostPagesClient from '@/components/LostPagesClient'
import { useEffect } from 'react'
import { Suspense } from 'react'

// Simple fallback component in case the main component fails
function FallbackComponent() {
  return (
    <main className="min-h-screen flex items-center justify-center px-6 py-12 bg-white dark:bg-neutral-900 text-neutral-900 dark:text-neutral-100 transition-colors">
      <div className="max-w-xl w-full space-y-6 text-center">
        <h1 className="text-3xl sm:text-4xl font-bold tracking-tight">
          Page Not Found
        </h1>
        <p className="text-base sm:text-lg text-neutral-700 dark:text-neutral-300">
          The page you're looking for doesn't exist or has been moved.
        </p>
        <div className="flex justify-center gap-4 pt-4">
          <a href="/" className="px-4 py-2 rounded-xl border border-neutral-400 dark:border-neutral-700 hover:bg-neutral-100 dark:hover:bg-neutral-800 transition">
            Return Home
          </a>
        </div>
      </div>
    </main>
  )
}

export default function NotFound() {
  useEffect(() => {
    // Apply theme from localStorage
    try {
      const savedTheme = localStorage.getItem('theme')
      if (savedTheme === 'dark') {
        document.documentElement.classList.add('dark')
      } else {
        document.documentElement.classList.remove('dark')
      }
    } catch (error) {
      // Fallback if localStorage is not available
      console.warn('Could not access localStorage for theme');
    }
  }, [])

  return (
    <>
      <title>Tom Pravetz – 404</title>
      <Suspense fallback={<FallbackComponent />}>
        <LostPagesClient />
      </Suspense>
    </>
  )
}
