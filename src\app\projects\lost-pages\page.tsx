import Image from 'next/image'
import Link from 'next/link'

export default function LostPagesProject() {
  return (
    <main className="max-w-5xl mx-auto px-6 py-16 space-y-20">
      {/* Hero */}
      <section className="flex flex-col-reverse md:flex-row items-center gap-12">
        <div className="space-y-6 max-w-xl">
          <h1 className="text-4xl font-extrabold">Lost Pages</h1>
          <p className="text-lg text-muted-foreground">
            Every broken link becomes a short AI story with a whimsical explanation. Because getting lost should be fun.
          </p>
          <Link
            href="/free-tacos"
            className="inline-block bg-violet-600 text-white px-5 py-2.5 rounded-lg shadow hover:bg-violet-500 transition"
          >
            🌐 See it live: /free-tacos
          </Link>
        </div>

        <div className="w-full md:w-1/2 rounded-xl overflow-hidden shadow-lg">
          <Image
            src="/projects/lost-pages/lost-pages-hero.png"
            alt="Sample Lost Page output"
            width={800}
            height={600}
            className="w-full h-auto object-cover"
          />
        </div>
      </section>

      {/* How It Works */}
      <section className="grid md:grid-cols-2 gap-12">
        <div>
          <h2 className="text-2xl font-bold mb-4">🛠️ How It Works</h2>
          <ul className="list-disc list-inside space-y-2 text-muted-foreground">
            <li>Intercepts 404 errors using Next.js App Router</li>
            <li>Sends the slug to Mistral AI to generate a creative story</li>
            <li>Uses intelligent fallbacks if the API times out</li>
            <li>Returns a whimsical, AI-generated 404 experience</li>
            <li>Caches responses to improve performance</li>
          </ul>
        </div>

        <div>
          <h2 className="text-2xl font-bold mb-4">🧠 Tech Stack</h2>
          <div className="flex flex-wrap gap-3">
            <span className="bg-zinc-100 dark:bg-zinc-800 px-4 py-1 rounded-full text-sm">🧠 Mistral AI</span>
            <span className="bg-zinc-100 dark:bg-zinc-800 px-4 py-1 rounded-full text-sm">⚡ Next.js</span>
            <span className="bg-zinc-100 dark:bg-zinc-800 px-4 py-1 rounded-full text-sm">🌀 Tailwind CSS</span>
            <span className="bg-zinc-100 dark:bg-zinc-800 px-4 py-1 rounded-full text-sm">📦 TypeScript</span>
            <span className="bg-zinc-100 dark:bg-zinc-800 px-4 py-1 rounded-full text-sm">💾 In-memory Cache</span>
          </div>
        </div>
      </section>

      {/* Sample Output */}
      <section className="bg-zinc-100 dark:bg-zinc-900 p-8 rounded-xl shadow space-y-6">
        <h2 className="text-2xl font-bold">📝 Sample Lost Page</h2>
        <p className="text-muted-foreground">
            Here's what you might see if you visit a broken link like <code>/chatgpt</code>.
        </p>

        <div className="bg-white dark:bg-zinc-800 p-6 rounded-lg shadow space-y-4 max-w-2xl mx-auto">
          <h3 className="text-xl font-bold text-violet-400">The Algorithm Has Gone Walkabout – Seeking Data Streams!</h3>
          <p className="text-muted-foreground">
              It appears our resident data-stream retriever has briefly wandered into a pocket dimension populated entirely by sentient rubber ducks.
              We're attempting to establish communication, but reception is... patchy.
          </p>
          <p className="text-sm text-zinc-400 uppercase tracking-wide font-medium">Classification: <span className="text-white dark:text-zinc-100">Quantum Echo</span></p>
          <p className="text-sm italic text-amber-500">
              💡 Try refreshing your browser while simultaneously whistling the theme song to a 1980s infomercial.
          </p>
        </div>
      </section>

      {/* What's Next */}
      <section>
        <h2 className="text-2xl font-bold mb-4">🔮 What's Next</h2>
        <ul className="list-disc list-inside space-y-2 text-muted-foreground">
          <li>Add more creative fallback responses</li>
          <li>Improve caching and performance</li>
          <li>Save/share options for memorable Lost Pages</li>
        </ul>
      </section>
      
      {/* Behind the Scenes */}
      <section className="space-y-4">
        <h2 className="text-2xl font-bold">🧪 Behind the Scenes</h2>
        <p className="text-muted-foreground">
            This is the prompt I use to instruct Mistral AI to generate a Lost Page.
            The response includes a title, reason, classification, and recovery tip — all returned in structured JSON.
        </p>

        <details className="mt-4">
            <summary className="cursor-pointer text-sm underline text-muted-foreground hover:text-white">
            🧠 View the full AI prompt
            </summary>
            <pre className="mt-2 p-4 bg-zinc-900 rounded-md text-sm text-zinc-200 overflow-x-auto whitespace-pre-wrap">
        {`You are a whimsical AI who returns creative 404 page messages. Your output must always be valid JSON with the following four fields:

- "title": a silly title (under 12 words)
- "reason": a short reason why the page is missing
- "classification": a funny category like "Temporal Anomaly"
- "recoveryTip": a joke suggestion to recover the page

If the URL slug includes inappropriate, hateful, or explicit language, DO NOT mention it. Just make a generic joke about reality breaking down, censorship, or forbidden knowledge. Never refuse the request. Just use silly fiction.

Always respond ONLY with valid JSON. Do not include markdown or triple backticks.

Example:
{
  "title": "Reality Overflow: Page Poured Into Another Universe",
  "reason": "A curious temporal raccoon unplugged the stability protocols.",
  "classification": "Dimensional Glitch",
  "recoveryTip": "Try distracting it with cosmic peanut butter."
}`}
            </pre>
        </details>
      </section>
    </main>
  )
}
