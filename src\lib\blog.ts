// /lib/blog.ts — this is a SERVER FILE only

import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'
import { marked } from 'marked'

const postsDirectory = path.join(process.cwd(), 'content/blog')

export function getAllPosts() {
  const fileNames = fs.readdirSync(postsDirectory)

  return fileNames.map((fileName) => {
    const slug = fileName.replace(/\.md$/, '')
    const fullPath = path.join(postsDirectory, fileName)
    const fileContents = fs.readFileSync(fullPath, 'utf8')

    const { data, content } = matter(fileContents)
    const wordCount = content.trim().split(/\s+/).length
    const readTime = `${Math.max(1, Math.ceil(wordCount / 200))} min read`

    return {
      slug,
      title: data.title || 'Untitled',
      date: data.date,
      excerpt: data.excerpt || '',
      coverImage: data.coverImage || null,
      ogImage: data.ogImage || null,
      tags: data.tags || [],
      featured: data.featured || false,
      readTime,
    }
  }).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
}

export function getPostBySlug(slug: string) {
  const fullPath = path.join(postsDirectory, `${slug}.md`)
  if (!fs.existsSync(fullPath)) return null

  const fileContents = fs.readFileSync(fullPath, 'utf8')
  const { data, content } = matter(fileContents)

  const contentHtml = marked.parse(content)
  const wordCount = content.trim().split(/\s+/).length
  const readTime = `${Math.max(1, Math.ceil(wordCount / 200))} min read`

  return {
    slug,
    title: data.title,
    date: data.date,
    excerpt: data.excerpt,
    coverImage: data.coverImage || null,
    ogImage: data.ogImage || null,
    tags: data.tags || [],
    featured: data.featured || false,
    readTime,
    contentHtml,
  }
}
