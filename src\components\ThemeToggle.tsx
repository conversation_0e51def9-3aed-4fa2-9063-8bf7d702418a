'use client'

import { useEffect, useState } from 'react'

export default function ThemeToggle() {
  const [isDark, setIsDark] = useState(false)

  useEffect(() => {
    setIsDark(document.documentElement.classList.contains('dark'))
  }, [])

  const toggleTheme = () => {
    const html = document.documentElement
    const isCurrentlyDark = html.classList.contains('dark')

    if (isCurrentlyDark) {
      html.classList.remove('dark')
      localStorage.theme = 'light'
      setIsDark(false)
    } else {
      html.classList.add('dark')
      localStorage.theme = 'dark'
      setIsDark(true)
    }
  }

  return (
    <button onClick={toggleTheme} className="p-2 rounded-md border border-zinc-300 dark:border-zinc-700 hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors">
      {isDark ? '☀️' : '🌙'}
    </button>
  )
}