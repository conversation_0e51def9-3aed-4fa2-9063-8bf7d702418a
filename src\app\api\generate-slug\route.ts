import { NextResponse } from 'next/server'
import { generateLostSlug } from '@/lib/lostPageEngine'

// Set a timeout for the API request
const API_TIMEOUT = 10000; // 10 seconds

export async function GET(req: Request) {
  try {
    const currentSlug = new URL(req.url).searchParams.get('current')
    let newSlug: string

    // Create a timeout promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Request timeout')), API_TIMEOUT);
    });

    // Generate a new slug with timeout
    try {
      // Race the actual request against the timeout
      const generateSlugWithRetry = async () => {
        let attempts = 0;
        let slug;

        do {
          if (attempts > 3) {
            throw new Error('Failed to generate a unique slug after multiple attempts');
          }

          slug = await generateLostSlug();
          attempts++;
        } while (slug === currentSlug);

        return slug;
      };

      newSlug = await Promise.race([
        generateSlugWithRetry(),
        timeoutPromise
      ]);
    } catch (error) {
      if (error instanceof Error && error.message === 'Request timeout') {
        return NextResponse.json({ error: 'Request timed out' }, { status: 504 });
      }
      throw error;
    }

    return NextResponse.json({ slug: newSlug });
  } catch (err) {
    console.error('[generate-slug API] Failed to generate slug:', err);
    return NextResponse.json({ error: 'Failed to generate slug' }, { status: 500 });
  }
}