import { NextRequest, NextResponse } from 'next/server'
import { generateLostPage } from '@/lib/lostPageEngine'

// Set a timeout for the API request (Vercel has a 10s limit)
const API_TIMEOUT = 5000; // 5 seconds to leave time for other processing

export async function POST(req: NextRequest) {
  try {
    // Parse the request body with error handling
    let slug: string;
    try {
      const body = await req.json();
      slug = body.slug;
    } catch (error) {
      return NextResponse.json({ error: 'Invalid request body' }, { status: 400 });
    }

    // Validate the slug
    if (!slug || typeof slug !== 'string') {
      return NextResponse.json({ error: 'Missing or invalid slug' }, { status: 400 });
    }

    // Create a timeout promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Request timeout')), API_TIMEOUT);
    });

    // Race the actual request against the timeout
    const result = await Promise.race([
      generateLostPage(slug),
      timeoutPromise
    ]);

    if ('error' in result) {
      return NextResponse.json(result, { status: 500 });
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('[API] Lost page generation error:', error);

    if (error instanceof Error && error.message === 'Request timeout') {
      return NextResponse.json({ error: 'Request timed out' }, { status: 504 });
    }

    return NextResponse.json(
      { error: 'Failed to generate lost page' },
      { status: 500 }
    );
  }
}
