import './globals.css'
import Navbar from '@/components/Navbar'

export const metadata = {
  title: '<PERSON> – Portfolio',
  description: 'AI tools, experiments, and dev logs',
  icons: {
    icon: '/favicon.ico',
  },
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body className="bg-white text-black dark:bg-black dark:text-white transition-colors overflow-x-hidden">
        <Navbar />
        {children}
      </body>
    </html>
  )
}