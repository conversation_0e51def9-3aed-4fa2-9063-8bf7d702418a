{"2": {"inputs": {"text": "", "clip": ["6", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "3": {"inputs": {"seed": 213525410936836, "steps": 20, "cfg": 8, "sampler_name": "euler", "scheduler": "normal", "denoise": 1, "model": ["6", 0], "positive": ["2", 0], "negative": ["9", 0], "latent_image": ["8", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"samples": ["3", 0], "vae": ["6", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "5": {"inputs": {"filename_prefix": "lost-page", "images": ["4", 0]}, "class_type": "SaveImage", "_meta": {"title": "SaveImage"}}, "6": {"inputs": {"ckpt_name": "dreamshaper_8.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "8": {"inputs": {"width": 512, "height": 512, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "9": {"inputs": {"text": "", "clip": ["6", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}}