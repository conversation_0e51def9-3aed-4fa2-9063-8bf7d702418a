'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useState } from 'react'
import ThemeToggle from './ThemeToggle'

const links = [
  { href: '/', label: 'Home' },
  { href: '/projects', label: 'Projects' },
  { href: '/blog', label: 'Blog' },
]

export default function Navbar() {
  const pathname = usePathname()
  const [menuOpen, setMenuOpen] = useState(false)

  return (
    <nav className="sticky top-0 z-50 w-full backdrop-blur bg-white/80 dark:bg-zinc-900/80 border-b border-zinc-200 dark:border-zinc-800">
      <div className="max-w-6xl mx-auto flex items-center px-6 py-4">
        {/* Left: Logo */}
        <Link href="/" className="text-xl font-bold tracking-tight hover:opacity-80 transition">
          tomprav.dev
        </Link>

        {/* Spacer to push nav to the right */}
        <div className="flex-grow" />

        {/* Right: Desktop nav */}
        <div className="hidden sm:flex items-center gap-4">
          {links.map(({ href, label }) => (
            <Link
              key={href}
              href={href}
              className={`text-sm font-medium hover:underline underline-offset-4 ${
                pathname === href ? 'text-indigo-600 dark:text-indigo-400' : 'text-zinc-600 dark:text-zinc-300'
              }`}
            >
              {label}
            </Link>
          ))}

          <a
            href="https://github.com/tompravetz"
            target="_blank"
            rel="noopener noreferrer"
            className="text-sm font-medium hover:underline underline-offset-4 text-zinc-600 dark:text-zinc-300"
          >
            GitHub
          </a>

          <ThemeToggle />
        </div>

        {/* Mobile menu button (only shows on small screens) */}
        <button
          onClick={() => setMenuOpen(!menuOpen)}
          className="sm:hidden text-2xl focus:outline-none ml-4"
          aria-label="Toggle Menu"
        >
          {menuOpen ? '✖️' : '☰'}
        </button>
      </div>

      {/* Mobile menu dropdown */}
      {menuOpen && (
        <div className="sm:hidden px-6 pb-4 flex flex-col gap-3">
          {links.map(({ href, label }) => (
            <Link
              key={href}
              href={href}
              onClick={() => setMenuOpen(false)}
              className={`text-sm font-medium hover:underline underline-offset-4 ${
                pathname === href ? 'text-indigo-600 dark:text-indigo-400' : 'text-zinc-600 dark:text-zinc-300'
              }`}
            >
              {label}
            </Link>
          ))}

          <a
            href="https://github.com/tompravetz"
            target="_blank"
            rel="noopener noreferrer"
            className="text-sm font-medium hover:underline underline-offset-4 text-zinc-600 dark:text-zinc-300"
          >
            GitHub
          </a>

          <ThemeToggle />
        </div>
      )}
    </nav>
  )
}
